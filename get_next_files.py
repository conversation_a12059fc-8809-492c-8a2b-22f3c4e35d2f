import json

with open('data/issues.json', 'r', encoding='utf-8') as f:
    issues_dict = json.load(f)

all_files = list(issues_dict.keys())
print(f'总文件数: {len(all_files)}')
print()

# 从第30个文件开始显示（索引29）
for i, file_path in enumerate(all_files[29:39], 30):
    print(f'第{i}个文件: {file_path}')
    issues = issues_dict[file_path]
    for issue in issues[:2]:
        print(f'  - {issue["rule"]}: {issue["message"]} (第{issue["line"]}行)')
    if len(issues) > 2:
        print(f'  ... 还有{len(issues)-2}个问题')
    print() 
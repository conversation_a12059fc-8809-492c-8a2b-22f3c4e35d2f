import json
import sys

if len(sys.argv) != 2:
    print("Usage: python3 get_file_issues.py <file_number>")
    sys.exit(1)

file_number = int(sys.argv[1])
index = file_number - 1

with open('data/issues.json', 'r', encoding='utf-8') as f:
    issues_dict = json.load(f)

file_paths = list(issues_dict.keys())
print(f"总文件数: {len(file_paths)}")

if index < len(file_paths):
    file_path = file_paths[index]
    issues = issues_dict[file_path]
    print(f"文件 {file_number}: {file_path}")
    print(f"问题数量: {len(issues)}")
    for i, issue in enumerate(issues, 1):
        print(f"  问题{i}: {issue['rule']}:{issue['line']} {issue['message']}")
else:
    print(f"没有第{file_number}个文件") 
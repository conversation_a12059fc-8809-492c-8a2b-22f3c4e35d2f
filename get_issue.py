import json
import sys

if len(sys.argv) != 2:
    print("Usage: python3 get_issue.py <file_number>")
    sys.exit(1)

file_number = int(sys.argv[1])
index = file_number - 1

with open('data/issues.json', 'r', encoding='utf-8') as f:
    issues_dict = json.load(f)

# 转换为列表格式，每个元素包含file_path, checker, line_no, description
issues_list = []
for file_path, issues in issues_dict.items():
    for issue in issues:
        issues_list.append({
            'file_path': file_path,
            'checker': issue['rule'],
            'line_no': issue['line'],
            'description': issue['message']
        })

print(f"总问题数: {len(issues_list)}")
print(f"总文件数: {len(issues_dict)}")

if index < len(issues_list):
    issue = issues_list[index]
    print(f"问题 {file_number}: {issue['file_path']}")
    print(f"问题: {issue['checker']}:{issue['line_no']} {issue['description']}")
else:
    print(f"没有第{file_number}个问题") 
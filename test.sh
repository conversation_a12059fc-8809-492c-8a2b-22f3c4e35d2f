function get_issues() {
  local project_id=$1
  local output_file=$2
  local rules=$3
  local page=1
  local page_size=500
  local total=""

  # 清空输出文件
  > "$output_file"

  while true; do
    # 调用接口获取问题列表
    response=$(curl -s -X POST "${THEMIS_HOST}/api/quality/local/v2/platform/issue/list" \
      -H "Content-Type: application/json" \
      --data "{
        \"gitProjectId\": \"$project_id\",
        \"branch\": \"\",
        \"severities\": null,
        \"authors\": null,
        \"p\": $page,
        \"ps\": $page_size,
        \"statuses\": null,
        \"types\": \"BUG,VULNERABILITY\",
        \"rules\": \"$rules\"
      }")

    # 检查接口调用是否成功
    if [[ $? -ne 0 ]]; then
      echo "调用问题列表接口失败, response is $response"
      return 1
    fi

    # 解析响应
    status=$(echo "$response" | jq -r '.status')
    if [[ "$status" != "200" ]]; then
      echo "接口返回错误: $(echo "$response" | jq -r '.message')"
      return 1
    fi

    # 获取总记录数
    current_total=$(echo "$response" | jq -r '.data.total')
    if [[ -z "$total" ]]; then
      total=$current_total
    fi

    # 如果total为0，直接返回
    if [[ $total -eq 0 ]]; then
      return 0
    fi

    # 处理问题列表
    issues_count=$(echo "$response" | jq -r '.data.issues | length')
    if [[ $issues_count -gt 0 ]]; then
      echo "$response" | jq -r '.data.issues[]' >> "$output_file"
    fi

    # 判断是否还有下一页
    if [[ $((page * page_size)) -ge $total ]]; then
      break
    fi
    page=$((page + 1))
  done

  return 0
}